package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjContractMapper;
import com.xinghuo.project.entity.ProjContractDatelogEntity;
import com.xinghuo.project.entity.ProjContractEntity;
import com.xinghuo.project.model.contract.ProjContractPagination;
import com.xinghuo.project.service.ProjContractDatelogService;
import com.xinghuo.project.service.ProjContractMemberService;
import com.xinghuo.project.service.ProjContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 合同服务实现类
 */
@Service
public class ProjContractServiceImpl extends BaseServiceImpl<ProjContractMapper, ProjContractEntity> implements ProjContractService {

    @Autowired
    private ProjContractDatelogService datelogService;

    @Autowired
    private ProjContractMemberService memberService;

    @Override
    public List<ProjContractEntity> getList(ProjContractPagination pagination) {
        QueryWrapper<ProjContractEntity> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<ProjContractEntity> lambda = queryWrapper.lambda();
        
        // 根据合同名称模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getName())) {
            lambda.like(ProjContractEntity::getName, pagination.getName());
        }
        
        // 根据合同编号模糊查询
        if (StrXhUtil.isNotEmpty(pagination.getCNo())) {
            lambda.like(ProjContractEntity::getCno, pagination.getCNo());
        }
        
        // 根据客户ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getCustId())) {
            lambda.eq(ProjContractEntity::getCustId, pagination.getCustId());
        }
        
        // 根据负责人ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getOwnId())) {
            lambda.eq(ProjContractEntity::getOwnId, pagination.getOwnId());
        }
        
        // 根据合同状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getContractStatus())) {
            lambda.eq(ProjContractEntity::getContractStatus, pagination.getContractStatus());
        }
        
        // 根据收款状态精确查询
        if (StrXhUtil.isNotEmpty(pagination.getMoneyStatus())) {
            lambda.eq(ProjContractEntity::getMoneyStatus, pagination.getMoneyStatus());
        }
        
        // 根据签订年份精确查询
        if (pagination.getSignYear() != null) {
            lambda.eq(ProjContractEntity::getSignYear, pagination.getSignYear());
        }
        
        // 根据金额范围查询
        if (pagination.getMinAmount() != null) {
            lambda.ge(ProjContractEntity::getAmount, pagination.getMinAmount());
        }
        if (pagination.getMaxAmount() != null) {
            lambda.le(ProjContractEntity::getAmount, pagination.getMaxAmount());
        }
        
        // 根据签订日期范围查询
        if (pagination.getSignDateStart() != null) {
            lambda.ge(ProjContractEntity::getSignDate, pagination.getSignDateStart());
        }
        if (pagination.getSignDateEnd() != null) {
            lambda.le(ProjContractEntity::getSignDate, pagination.getSignDateEnd());
        }
        
        // 根据部门ID精确查询
        if (StrXhUtil.isNotEmpty(pagination.getDeptId())) {
            lambda.eq(ProjContractEntity::getDeptId, pagination.getDeptId());
        }
        
        // 根据关键字搜索合同名称或编号
        String keyword = pagination.getKeyword();
        if (StrXhUtil.isNotEmpty(keyword)) {
            lambda.and(wrapper -> wrapper
                    .like(ProjContractEntity::getName, keyword)
                    .or()
                    .like(ProjContractEntity::getCno, keyword)
            );
        }
        
        // 排除已删除的记录
        lambda.isNull(ProjContractEntity::getDeleteMark);
        
        // 排序
        lambda.orderByDesc(ProjContractEntity::getCreateTime);
        
        // 分页
        return  processDataType(queryWrapper,pagination);

    }

    @Override
    public List<ProjContractEntity> getListByCustomerId(String customerId) {
        LambdaQueryWrapper<ProjContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractEntity::getCustId, customerId);
        queryWrapper.eq(ProjContractEntity::getDeleteMark, 0);
        queryWrapper.orderByDesc(ProjContractEntity::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public ProjContractEntity getInfo(String id) {
        return this.getById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjContractEntity entity) {
        String contractId = RandomUtil.snowId();
        entity.setId(contractId);
        entity.setDeleteMark(0);

        // 设置合同年度
        if (entity.getSignDate() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(entity.getSignDate());
            entity.setSignYear(calendar.get(Calendar.YEAR));
        }

        // 设置初始状态
        if (StrXhUtil.isEmpty(entity.getContractStatus())) {
            entity.setContractStatus("draft"); // 草稿
        }
        if (StrXhUtil.isEmpty(entity.getMoneyStatus())) {
            entity.setMoneyStatus("unpaid"); // 未收款
        }

        // 设置初始金额
        if (entity.getYsAmount() == null) {
            entity.setYsAmount(java.math.BigDecimal.ZERO);
        }
        if (entity.getYearYsAmount() == null) {
            entity.setYearYsAmount(java.math.BigDecimal.ZERO);
        }

        // 计算预估毛利和毛利率
        calculateProfit(entity);

        this.save(entity);

        // 初始化默认项目成员（合同负责人作为项目经理）
        if (StrXhUtil.isNotEmpty(entity.getOwnId())) {
            try {
                memberService.initDefaultMembers(contractId, entity.getOwnId());
            } catch (Exception e) {
                log.warn("初始化合同默认成员失败，合同ID: {}, 负责人ID: {}", contractId, entity.getOwnId(), e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjContractEntity entity) {
        ProjContractEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("合同不存在");
        }
        
        entity.setId(id);
        
        // 更新合同年度
        if (entity.getSignDate() != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(entity.getSignDate());
            entity.setSignYear(calendar.get(Calendar.YEAR));
        }
        
        // 计算预估毛利和毛利率
        calculateProfit(entity);
        
        this.updateById(entity);
    }

    @Override
    public void delete(String id) {
        // 逻辑删除
        ProjContractEntity entity = new ProjContractEntity();
        entity.setId(id);
        entity.setDeleteMark(1);
        this.updateById(entity);
    }

    @Override
    public boolean isExistByCNo(String cNo, String id) {
        LambdaQueryWrapper<ProjContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractEntity::getCno, cNo);
        queryWrapper.eq(ProjContractEntity::getDeleteMark, 0);
        
        // 如果是更新操作，需要排除自身
        if (StrXhUtil.isNotEmpty(id)) {
            queryWrapper.ne(ProjContractEntity::getId, id);
        }
        
        return this.count(queryWrapper) > 0;
    }

    @Override
    public void updateStatus(String id, String status) {
        ProjContractEntity entity = new ProjContractEntity();
        entity.setId(id);
        entity.setContractStatus(status);
        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDate(String id, String dateType, Date newDate, String note) {
        ProjContractEntity entity = this.getById(id);
        if (entity == null) {
            throw new RuntimeException("合同不存在");
        }
        
        // 获取旧日期
        Date oldDate = null;
        
        // 根据日期类型更新对应字段
        switch (dateType) {
            case "signDate":
                oldDate = entity.getSignDate();
                entity.setSignDate(newDate);
                // 更新合同年度
                if (newDate != null) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(newDate);
                    entity.setSignYear(calendar.get(Calendar.YEAR));
                }
                break;
            case "bidDate":
                oldDate = entity.getBidDate();
                entity.setBidDate(newDate);
                break;
            case "commencementDate":
                oldDate = entity.getCommencementDate();
                entity.setCommencementDate(newDate);
                break;
            case "initialCheckDate":
                oldDate = entity.getInitialCheckDate();
                entity.setInitialCheckDate(newDate);
                break;
            case "finalCheckDate":
                oldDate = entity.getFinalCheckDate();
                entity.setFinalCheckDate(newDate);
                break;
            case "auditDate":
                oldDate = entity.getAuditDate();
                entity.setAuditDate(newDate);
                break;
            case "cstartDate":
                oldDate = entity.getCstartDate();
                entity.setCstartDate(newDate);
                break;
            case "cendDate":
                oldDate = entity.getCendDate();
                entity.setCendDate(newDate);
                break;
            case "mstartDate":
                oldDate = entity.getMstartDate();
                entity.setMstartDate(newDate);
                break;
            case "mendDate":
                oldDate = entity.getMendDate();
                entity.setMendDate(newDate);
                break;
            default:
                throw new RuntimeException("不支持的日期类型: " + dateType);
        }
        
        // 更新合同
        this.updateById(entity);
        
        // 记录日期变更日志
        ProjContractDatelogEntity datelog = new ProjContractDatelogEntity();
        datelog.setId(RandomUtil.snowId());
        datelog.setRelateId(id);
        datelog.setType(dateType);
        datelog.setOldDate(oldDate);
        datelog.setNewDate(newDate);
        datelog.setNote(note);
        datelogService.save(datelog);
    }
    
    /**
     * 计算预估毛利和毛利率
     *
     * @param entity 合同实体
     */
    private void calculateProfit(ProjContractEntity entity) {
        if (entity.getAmount() != null) {
            // 计算预估成本
            java.math.BigDecimal totalCost = java.math.BigDecimal.ZERO;
            if (entity.getEvaExternalAmount() != null) {
                totalCost = totalCost.add(entity.getEvaExternalAmount());
            }
            if (entity.getEvaCostAmount() != null) {
                totalCost = totalCost.add(entity.getEvaCostAmount());
            }
            
            // 计算预估毛利
            java.math.BigDecimal estProbit = entity.getAmount().subtract(totalCost);
            entity.setEstProbit(estProbit);
            
            // 计算预估毛利率
            if (entity.getAmount().compareTo(java.math.BigDecimal.ZERO) > 0) {
                java.math.BigDecimal estProbitRatio = estProbit.divide(entity.getAmount(), 4, java.math.RoundingMode.HALF_UP)
                        .multiply(new java.math.BigDecimal("100"));
                entity.setEstProbitRatio(estProbitRatio);
            }
        }
    }
}
