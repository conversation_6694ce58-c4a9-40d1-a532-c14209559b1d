-- 项目合同成员表
-- 用于管理项目合同的成员分配和角色权限

CREATE TABLE `zz_proj_contract_member` (
  `id` varchar(50) NOT NULL COMMENT '主键ID',
  `contract_id` varchar(50) NOT NULL COMMENT '合同ID，关联zz_proj_contract表',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID，关联sys_user表',
  `role_type` varchar(50) NOT NULL COMMENT '角色类型：contract_admin-合同管理员，pmo-PMO，project_manager-项目经理，team_member-项目组成员',
  `permissions` text COMMENT '权限范围，JSON格式存储具体权限配置',
  `is_primary` tinyint(1) DEFAULT '0' COMMENT '是否主要负责人：1-是，0-否',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator_user_id` varchar(50) DEFAULT NULL COMMENT '创建人ID',
  `creator_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `last_modified_user_id` varchar(50) DEFAULT NULL COMMENT '最后修改人ID',
  `last_modified_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `f_deletemark` tinyint(1) DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
  `f_tenantid` varchar(50) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_user` (`contract_id`,`user_id`,`f_deletemark`) COMMENT '合同用户唯一约束',
  KEY `idx_contract_id` (`contract_id`) COMMENT '合同ID索引',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  KEY `idx_role_type` (`role_type`) COMMENT '角色类型索引',
  KEY `idx_status` (`status`) COMMENT '状态索引',
  KEY `idx_is_primary` (`is_primary`) COMMENT '主要负责人索引',
  KEY `idx_creator_time` (`creator_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目合同成员表';

-- 插入示例数据（可选）
-- 注意：实际使用时需要根据真实的合同ID和用户ID来插入数据

-- 示例：为某个合同添加默认成员
-- INSERT INTO `zz_proj_contract_member` (
--   `id`, 
--   `contract_id`, 
--   `user_id`, 
--   `role_type`, 
--   `permissions`, 
--   `is_primary`, 
--   `status`, 
--   `remark`,
--   `creator_user_id`,
--   `f_deletemark`
-- ) VALUES (
--   'member_001', 
--   'contract_001', 
--   'user_001', 
--   'project_manager', 
--   '{"canEditBasic":false,"canEditBusiness":false,"canEditProject":true,"canEditDelivery":true,"canViewAll":true}', 
--   1, 
--   1, 
--   '系统自动分配的项目经理',
--   'system',
--   0
-- );

-- 创建相关索引以提高查询性能
-- 复合索引：合同ID + 角色类型
CREATE INDEX `idx_contract_role` ON `zz_proj_contract_member` (`contract_id`, `role_type`);

-- 复合索引：用户ID + 状态
CREATE INDEX `idx_user_status` ON `zz_proj_contract_member` (`user_id`, `status`);

-- 复合索引：合同ID + 主要负责人
CREATE INDEX `idx_contract_primary` ON `zz_proj_contract_member` (`contract_id`, `is_primary`);

-- 角色类型说明：
-- contract_admin: 合同管理员 - 可创建合同、编辑基本信息、分配信息
-- pmo: PMO - 可创建合同、编辑基本信息、分配信息  
-- project_manager: 项目经理 - 可维护交付里程碑、联系人信息
-- team_member: 项目组成员 - 只能查看项目进展和指定字段

-- 权限配置示例：
-- 合同管理员权限：
-- {"canEditBasic":true,"canEditBusiness":true,"canEditProject":true,"canEditDelivery":true,"canViewAll":true}

-- PMO权限：
-- {"canEditBasic":true,"canEditBusiness":true,"canEditProject":false,"canEditDelivery":false,"canViewAll":true}

-- 项目经理权限：
-- {"canEditBasic":false,"canEditBusiness":false,"canEditProject":true,"canEditDelivery":true,"canViewAll":true}

-- 项目组成员权限：
-- {"canEditBasic":false,"canEditBusiness":false,"canEditProject":false,"canEditDelivery":false,"canViewAll":false}
