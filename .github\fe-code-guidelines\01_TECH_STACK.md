# xace-web-vue3 核心技术栈与架构

## 核心技术栈

* **框架:** Vue 3.3.x
* **开发语言:** TypeScript 5.x
* **构建工具:** Vite 4.x
* **UI组件库:** Ant Design Vue 3.2.x
* **状态管理:** Pinia 2.x
* **路由管理:** Vue Router 4.x
* **HTTP客户端:** Axios
* **CSS预处理器:** Less
* **工具库:**
  * Lodash-es (工具函数)
  * Dayjs (日期处理)
  * Echarts (图表)
  * VueUse (组合式API工具集)
  * Sortablejs (拖拽排序)

## 开发环境

* **Node.js:** 16.15.0+
* **包管理器:** pnpm 8.1.0+（推荐）或 yarn
* **IDE推荐:** VS Code 配合以下插件：
  * Vue Language Features (Volar)
  * TypeScript Vue Plugin (Volar)
  * ESLint
  * Prettier
  * i18n Ally（国际化支持）

## 项目架构

* **组件设计模式:** Composition API + `<script setup>` 语法
* **状态管理模式:** Store模式 (Pinia)
* **API调用模式:** 基于Promise的服务层封装
* **国际化方案:** vue-i18n
* **权限控制:** 基于角色的访问控制 (RBAC)
* **主题方案:** 基于CSS变量的动态主题切换

## 格式化与代码质量

* **Prettier:** 代码格式化
* **ESLint:** 代码质量检查
* **StyleLint:** 样式代码规范检查
* **CommitLint:** Git提交信息规范
* **Husky:** Git钩子管理
* **lint-staged:** 提交前代码检查

## 关键依赖版本

```json
{
  "vue": "3.3.4",
  "pinia": "2.1.3",
  "vue-router": "4.4.0",
  "ant-design-vue": "3.2.20",
  "typescript": "5.0.4",
  "vite": "4.3.8",
  "axios": "1.4.0",
  "vue-i18n": "9.2.2",
  "@vueuse/core": "10.1.2"
}
```

## 性能优化策略

* 路由懒加载
* 组件按需导入
* 大型依赖库按需引入
* 图片资源优化（WebP格式、适当压缩）
* 虚拟滚动处理大数据列表
* 合理使用缓存策略

## 浏览器兼容性

项目支持以下现代浏览器:

* Chrome (最新2个版本)
* Firefox (最新2个版本)
* Edge (最新2个版本)
* Safari (最新2个版本)

> 不支持Internet Explorer浏览器

## 开发规范要点

* 使用TypeScript类型定义提高代码健壮性
* 组件设计遵循单一职责原则
* 使用Composition API提高代码复用性
* 遵循ESLint和StyleLint规则
* 编写单元测试确保代码质量

## 参考文档

* [Vue 3 官方文档](https://cn.vuejs.org/)
* [TypeScript 官方文档](https://www.typescriptlang.org/)
* [Vite 官方文档](https://cn.vitejs.dev/)
* [Ant Design Vue 文档](https://antdv.com/)
* [Pinia 官方文档](https://pinia.vuejs.org/zh/)
* [Vue Router 官方文档](https://router.vuejs.org/zh/)
* [VueUse 官方文档](https://vueuse.org/)
* [Vue I18n 官方文档](https://vue-i18n.intlify.dev/)
