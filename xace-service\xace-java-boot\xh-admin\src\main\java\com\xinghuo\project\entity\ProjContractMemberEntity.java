package com.xinghuo.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xinghuo.common.base.entity.BaseEntityV2;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 项目合同成员实体类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@EqualsAndHashCode
@TableName("zz_proj_contract_member")
public class ProjContractMemberEntity  extends BaseEntityV2.CUBaseEntityV2 {


    /**
     * 合同ID
     * 关联ProjContractEntity
     */
    @TableField("contract_id")
    private String contractId;

    /**
     * 用户ID
     * 关联SysUserEntity
     */
    @TableField("user_id")
    private String userId;

    /**
     * 角色类型
     * contract_admin: 合同管理员
     * pmo: PMO
     * project_manager: 项目经理
     * team_member: 项目组成员
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 权限范围
     * JSON格式存储具体权限配置
     * 例如: {"canEditBasic": true, "canEditBusiness": false, "canViewOnly": false}
     */
    @TableField("permissions")
    private String permissions;

    /**
     * 是否主要负责人
     * 1: 是, 0: 否
     */
    @TableField("is_primary")
    private Integer isPrimary;

    /**
     * 状态
     * 1: 启用, 0: 禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
