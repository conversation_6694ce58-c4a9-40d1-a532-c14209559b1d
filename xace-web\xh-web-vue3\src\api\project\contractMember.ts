import { defHttp } from '/@/utils/http/axios';
import { ListResult } from '/@/api/model/baseModel';

/**
 * 项目合同成员管理API
 */

// API URL前缀
const API_PREFIX = '/api/project/contract/member';

/**
 * 项目合同成员对象接口
 */
export interface ContractMemberModel {
  id: string;
  contractId: string;
  contractName?: string;
  contractNo?: string;
  userId: string;
  userName?: string;
  userAccount?: string;
  userDeptName?: string;
  roleType: string;
  roleTypeText?: string;
  permissions: string;
  isPrimary: number;
  isPrimaryText?: string;
  status: number;
  statusText?: string;
  remark?: string;
  creatorUserId: string;
  creatorUserName?: string;
  creatorTime: string;
  lastModifiedUserId: string;
  lastModifiedUserName?: string;
  lastModifiedTime: string;
}

/**
 * 项目合同成员表单接口
 */
export interface ContractMemberFormModel {
  contractId: string;
  userId: string;
  roleType: string;
  permissions?: string;
  isPrimary?: number;
  status: number;
  remark?: string;
}

/**
 * 角色类型枚举
 */
export enum RoleTypeEnum {
  CONTRACT_ADMIN = 'contract_admin',
  PMO = 'pmo',
  PROJECT_MANAGER = 'project_manager',
  TEAM_MEMBER = 'team_member',
}

/**
 * 角色类型文本映射
 */
export const RoleTypeTextMap = {
  [RoleTypeEnum.CONTRACT_ADMIN]: '合同管理员',
  [RoleTypeEnum.PMO]: 'PMO',
  [RoleTypeEnum.PROJECT_MANAGER]: '项目经理',
  [RoleTypeEnum.TEAM_MEMBER]: '项目组成员',
};

/**
 * 默认权限配置
 */
export const DefaultPermissions = {
  [RoleTypeEnum.CONTRACT_ADMIN]: {
    canEditBasic: true,
    canEditBusiness: true,
    canEditProject: true,
    canEditDelivery: true,
    canViewAll: true,
  },
  [RoleTypeEnum.PMO]: {
    canEditBasic: true,
    canEditBusiness: true,
    canEditProject: false,
    canEditDelivery: false,
    canViewAll: true,
  },
  [RoleTypeEnum.PROJECT_MANAGER]: {
    canEditBasic: false,
    canEditBusiness: false,
    canEditProject: true,
    canEditDelivery: true,
    canViewAll: true,
  },
  [RoleTypeEnum.TEAM_MEMBER]: {
    canEditBasic: false,
    canEditBusiness: false,
    canEditProject: false,
    canEditDelivery: false,
    canViewAll: false,
  },
};

/**
 * 根据合同ID获取成员列表
 * @param contractId 合同ID
 * @returns 成员列表
 */
export const getContractMemberList = (contractId: string) => {
  return defHttp.get<ContractMemberModel[]>({
    url: `${API_PREFIX}/contract/${contractId}`,
  });
};

/**
 * 根据用户ID获取参与的合同成员记录
 * @param userId 用户ID
 * @returns 成员记录列表
 */
export const getContractMemberListByUserId = (userId: string) => {
  return defHttp.get<ContractMemberModel[]>({
    url: `${API_PREFIX}/user/${userId}`,
  });
};

/**
 * 根据合同ID和角色类型获取成员列表
 * @param contractId 合同ID
 * @param roleType 角色类型
 * @returns 成员列表
 */
export const getContractMemberListByRole = (contractId: string, roleType: string) => {
  return defHttp.get<ContractMemberModel[]>({
    url: `${API_PREFIX}/contract/${contractId}/role/${roleType}`,
  });
};

/**
 * 获取合同成员详情
 * @param id 成员ID
 * @returns 成员详情
 */
export const getContractMemberInfo = (id: string) => {
  return defHttp.get<ContractMemberModel>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 创建合同成员
 * @param data 成员表单数据
 * @returns 操作结果
 */
export const createContractMember = (data: ContractMemberFormModel) => {
  return defHttp.post<void>({
    url: API_PREFIX,
    data,
  });
};

/**
 * 更新合同成员
 * @param id 成员ID
 * @param data 成员表单数据
 * @returns 操作结果
 */
export const updateContractMember = (id: string, data: ContractMemberFormModel) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/${id}`,
    data,
  });
};

/**
 * 删除合同成员
 * @param id 成员ID
 * @returns 操作结果
 */
export const deleteContractMember = (id: string) => {
  return defHttp.delete<void>({
    url: `${API_PREFIX}/${id}`,
  });
};

/**
 * 检查用户是否为合同成员
 * @param contractId 合同ID
 * @param userId 用户ID
 * @returns 是否为成员
 */
export const isContractMember = (contractId: string, userId: string) => {
  return defHttp.get<boolean>({
    url: `${API_PREFIX}/check/${contractId}/${userId}`,
  });
};

/**
 * 获取用户在指定合同中的角色类型
 * @param contractId 合同ID
 * @param userId 用户ID
 * @returns 角色类型
 */
export const getUserRoleInContract = (contractId: string, userId: string) => {
  return defHttp.get<string>({
    url: `${API_PREFIX}/role/${contractId}/${userId}`,
  });
};

/**
 * 获取用户在指定合同中的权限
 * @param contractId 合同ID
 * @param userId 用户ID
 * @returns 权限JSON字符串
 */
export const getUserPermissionsInContract = (contractId: string, userId: string) => {
  return defHttp.get<string>({
    url: `${API_PREFIX}/permissions/${contractId}/${userId}`,
  });
};

/**
 * 设置主要负责人
 * @param contractId 合同ID
 * @param userId 用户ID
 * @returns 操作结果
 */
export const setPrimaryMember = (contractId: string, userId: string) => {
  return defHttp.put<void>({
    url: `${API_PREFIX}/primary/${contractId}/${userId}`,
  });
};

/**
 * 初始化合同默认成员
 * @param contractId 合同ID
 * @param ownerId 合同负责人ID
 * @returns 操作结果
 */
export const initDefaultMembers = (contractId: string, ownerId: string) => {
  return defHttp.post<void>({
    url: `${API_PREFIX}/init/${contractId}/${ownerId}`,
  });
};
