package com.xinghuo.project.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xinghuo.common.base.service.BaseServiceImpl;
import com.xinghuo.common.util.core.RandomUtil;
import com.xinghuo.common.util.core.StrXhUtil;
import com.xinghuo.project.dao.ProjContractMemberMapper;
import com.xinghuo.project.entity.ProjContractMemberEntity;
import com.xinghuo.project.service.ProjContractMemberService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 项目合同成员服务实现类
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Service
public class ProjContractMemberServiceImpl extends BaseServiceImpl<ProjContractMemberMapper, ProjContractMemberEntity> 
        implements ProjContractMemberService {

    @Override
    public List<ProjContractMemberEntity> getListByContractId(String contractId) {
        if (StrXhUtil.isEmpty(contractId)) {
            return List.of();
        }
        
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getContractId, contractId);
        queryWrapper.eq(ProjContractMemberEntity::getStatus, 1);
        queryWrapper.orderByDesc(ProjContractMemberEntity::getIsPrimary);
        queryWrapper.orderByAsc(ProjContractMemberEntity::getCreatedAt);
        
        return this.list(queryWrapper);
    }

    @Override
    public List<ProjContractMemberEntity> getListByUserId(String userId) {
        if (StrXhUtil.isEmpty(userId)) {
            return List.of();
        }
        
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getUserId, userId);
        queryWrapper.eq(ProjContractMemberEntity::getStatus, 1);
        queryWrapper.orderByDesc(ProjContractMemberEntity::getCreatedAt);
        
        return this.list(queryWrapper);
    }

    @Override
    public ProjContractMemberEntity getByContractIdAndUserId(String contractId, String userId) {
        if (StrXhUtil.isEmpty(contractId) || StrXhUtil.isEmpty(userId)) {
            return null;
        }
        
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getContractId, contractId);
        queryWrapper.eq(ProjContractMemberEntity::getUserId, userId);
        queryWrapper.eq(ProjContractMemberEntity::getStatus, 1);
        
        return this.getOne(queryWrapper);
    }

    @Override
    public List<ProjContractMemberEntity> getListByContractIdAndRoleType(String contractId, String roleType) {
        if (StrXhUtil.isEmpty(contractId) || StrXhUtil.isEmpty(roleType)) {
            return List.of();
        }
        
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getContractId, contractId);
        queryWrapper.eq(ProjContractMemberEntity::getRoleType, roleType);
        queryWrapper.eq(ProjContractMemberEntity::getStatus, 1);
        
        return this.list(queryWrapper);
    }

    @Override
    public boolean isContractMember(String contractId, String userId) {
        return getByContractIdAndUserId(contractId, userId) != null;
    }

    @Override
    public ProjContractMemberEntity getPrimaryMemberByContractId(String contractId) {
        if (StrXhUtil.isEmpty(contractId)) {
            return null;
        }
        
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getContractId, contractId);
        queryWrapper.eq(ProjContractMemberEntity::getIsPrimary, 1);
        queryWrapper.eq(ProjContractMemberEntity::getStatus, 1);
        
        return this.getOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProjContractMemberEntity entity) {
        entity.setId(RandomUtil.snowId());
        entity.setStatus(1);

        
        // 设置默认权限
        if (StrXhUtil.isEmpty(entity.getPermissions())) {
            entity.setPermissions(getDefaultPermissions(entity.getRoleType()));
        }
        
        // 如果设置为主要负责人，需要取消其他主要负责人
        if (entity.getIsPrimary() != null && entity.getIsPrimary() == 1) {
            clearPrimaryMember(entity.getContractId());
        }
        
        this.save(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(String id, ProjContractMemberEntity entity) {
        ProjContractMemberEntity oldEntity = this.getById(id);
        if (oldEntity == null) {
            throw new RuntimeException("合同成员不存在");
        }
        
        entity.setId(id);

        // 如果设置为主要负责人，需要取消其他主要负责人
        if (entity.getIsPrimary() != null && entity.getIsPrimary() == 1) {
            clearPrimaryMember(oldEntity.getContractId());
        }
        
        this.updateById(entity);
    }

    @Override
    public void delete(String id) {
        // 逻辑删除
        ProjContractMemberEntity entity = new ProjContractMemberEntity();
        entity.setId(id);

        this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAddMembers(String contractId, List<ProjContractMemberEntity> members) {
        for (ProjContractMemberEntity member : members) {
            member.setContractId(contractId);
            create(member);
        }
    }

    @Override
    public String getUserRoleInContract(String contractId, String userId) {
        ProjContractMemberEntity member = getByContractIdAndUserId(contractId, userId);
        return member != null ? member.getRoleType() : null;
    }

    @Override
    public String getUserPermissionsInContract(String contractId, String userId) {
        ProjContractMemberEntity member = getByContractIdAndUserId(contractId, userId);
        return member != null ? member.getPermissions() : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setPrimaryMember(String contractId, String userId) {
        // 先清除现有主要负责人
        clearPrimaryMember(contractId);
        
        // 设置新的主要负责人
        ProjContractMemberEntity member = getByContractIdAndUserId(contractId, userId);
        if (member != null) {
            member.setIsPrimary(1);
            this.updateById(member);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void initDefaultMembers(String contractId, String ownerId) {
        // 检查是否已经初始化过
        List<ProjContractMemberEntity> existingMembers = getListByContractId(contractId);
        if (!existingMembers.isEmpty()) {
            return;
        }
        
        // 创建默认的项目经理成员（合同负责人）
        ProjContractMemberEntity ownerMember = new ProjContractMemberEntity();
        ownerMember.setContractId(contractId);
        ownerMember.setUserId(ownerId);
        ownerMember.setRoleType("project_manager");
        ownerMember.setIsPrimary(1);
        ownerMember.setRemark("系统自动分配的项目经理");
        
        create(ownerMember);
    }

    /**
     * 清除合同的主要负责人标记
     */
    private void clearPrimaryMember(String contractId) {
        LambdaQueryWrapper<ProjContractMemberEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjContractMemberEntity::getContractId, contractId);
        queryWrapper.eq(ProjContractMemberEntity::getIsPrimary, 1);

        List<ProjContractMemberEntity> primaryMembers = this.list(queryWrapper);
        for (ProjContractMemberEntity member : primaryMembers) {
            member.setIsPrimary(0);
            this.updateById(member);
        }
    }

    /**
     * 根据角色类型获取默认权限
     */
    private String getDefaultPermissions(String roleType) {
        switch (roleType) {
            case "contract_admin":
                return "{\"canEditBasic\":true,\"canEditBusiness\":true,\"canEditProject\":true,\"canEditDelivery\":true,\"canViewAll\":true}";
            case "pmo":
                return "{\"canEditBasic\":true,\"canEditBusiness\":true,\"canEditProject\":false,\"canEditDelivery\":false,\"canViewAll\":true}";
            case "project_manager":
                return "{\"canEditBasic\":false,\"canEditBusiness\":false,\"canEditProject\":true,\"canEditDelivery\":true,\"canViewAll\":true}";
            case "team_member":
                return "{\"canEditBasic\":false,\"canEditBusiness\":false,\"canEditProject\":false,\"canEditDelivery\":false,\"canViewAll\":false}";
            default:
                return "{\"canEditBasic\":false,\"canEditBusiness\":false,\"canEditProject\":false,\"canEditDelivery\":false,\"canViewAll\":false}";
        }
    }
}
