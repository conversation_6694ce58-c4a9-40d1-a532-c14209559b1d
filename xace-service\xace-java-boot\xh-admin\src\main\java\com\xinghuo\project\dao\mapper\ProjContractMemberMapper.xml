<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xinghuo.project.dao.ProjContractMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xinghuo.project.entity.ProjContractMemberEntity">
        <id column="id" property="id" />
        <result column="contract_id" property="contractId" />
        <result column="user_id" property="userId" />
        <result column="role_type" property="roleType" />
        <result column="permissions" property="permissions" />
        <result column="is_primary" property="isPrimary" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="f_created_by" property="createdBy" />
        <result column="f_created_at" property="createdAt" />
        <result column="f_last_updated_at" property="lastUpdatedAt" />
        <result column="f_last_updated_by" property="lastUpdatedBy" />
    </resultMap>

    <!-- 根据合同ID获取成员列表 -->
    <select id="getListByContractId" resultMap="BaseResultMap">
        SELECT * FROM zz_proj_contract_member 
        WHERE contract_id = #{contractId} 
        AND status = 1
        ORDER BY is_primary DESC, creator_time ASC
    </select>

    <!-- 根据用户ID获取参与的合同成员记录 -->
    <select id="getListByUserId" resultMap="BaseResultMap">
        SELECT * FROM zz_proj_contract_member 
        WHERE user_id = #{userId} 
        AND status = 1
        ORDER BY f_created_at DESC
    </select>

    <!-- 根据合同ID和用户ID获取成员记录 -->
    <select id="getByContractIdAndUserId" resultMap="BaseResultMap">
        SELECT * FROM zz_proj_contract_member 
        WHERE contract_id = #{contractId} 
        AND user_id = #{userId} 
        AND status = 1
        LIMIT 1
    </select>

    <!-- 根据合同ID和角色类型获取成员列表 -->
    <select id="getListByContractIdAndRoleType" resultMap="BaseResultMap">
        SELECT * FROM zz_proj_contract_member 
        WHERE contract_id = #{contractId} 
        AND role_type = #{roleType} 
        AND status = 1
        ORDER BY is_primary DESC, f_created_at ASC
    </select>

    <!-- 检查用户是否为合同成员 -->
    <select id="isContractMember" resultType="boolean">
        SELECT COUNT(1) > 0 FROM zz_proj_contract_member 
        WHERE contract_id = #{contractId} 
        AND user_id = #{userId} 
        AND status = 1
    </select>

    <!-- 获取合同的主要负责人 -->
    <select id="getPrimaryMemberByContractId" resultMap="BaseResultMap">
        SELECT * FROM zz_proj_contract_member 
        WHERE contract_id = #{contractId} 
        AND is_primary = 1 
        AND status = 1
        LIMIT 1
    </select>

</mapper>
