package com.xinghuo.project.model.contractmember;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 项目合同成员表单对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Schema(description = "项目合同成员表单对象")
public class ProjContractMemberForm {

    /**
     * 合同ID
     */
    @NotBlank(message = "合同ID不能为空")
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 角色类型
     * contract_admin: 合同管理员
     * pmo: PMO
     * project_manager: 项目经理
     * team_member: 项目组成员
     */
    @NotBlank(message = "角色类型不能为空")
    @Schema(description = "角色类型", allowableValues = {"contract_admin", "pmo", "project_manager", "team_member"})
    private String roleType;

    /**
     * 权限范围
     * JSON格式存储具体权限配置
     * 例如: {"canEditBasic": true, "canEditBusiness": false, "canViewOnly": false}
     */
    @Schema(description = "权限范围")
    private String permissions;

    /**
     * 是否主要负责人
     * 1: 是, 0: 否
     */
    @Schema(description = "是否主要负责人", allowableValues = {"0", "1"})
    private Integer isPrimary;

    /**
     * 状态
     * 1: 启用, 0: 禁用
     */
    @NotNull(message = "状态不能为空")
    @Schema(description = "状态", allowableValues = {"0", "1"})
    private Integer status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Schema(description = "备注")
    private String remark;
}
