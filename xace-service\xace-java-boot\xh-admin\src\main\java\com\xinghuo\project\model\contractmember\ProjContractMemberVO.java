package com.xinghuo.project.model.contractmember;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 项目合同成员视图对象
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Schema(description = "项目合同成员视图对象")
public class ProjContractMemberVO {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private String id;

    /**
     * 合同ID
     */
    @Schema(description = "合同ID")
    private String contractId;

    /**
     * 合同名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "合同名称")
    private String contractName;

    /**
     * 合同编号（非数据库字段，需要关联查询）
     */
    @Schema(description = "合同编号")
    private String contractNo;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private String userId;

    /**
     * 用户名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "用户名称")
    private String userName;

    /**
     * 用户账号（非数据库字段，需要关联查询）
     */
    @Schema(description = "用户账号")
    private String userAccount;

    /**
     * 用户部门（非数据库字段，需要关联查询）
     */
    @Schema(description = "用户部门")
    private String userDeptName;

    /**
     * 角色类型
     * contract_admin: 合同管理员
     * pmo: PMO
     * project_manager: 项目经理
     * team_member: 项目组成员
     */
    @Schema(description = "角色类型")
    private String roleType;

    /**
     * 角色类型文本（非数据库字段，需要转换）
     */
    @Schema(description = "角色类型文本")
    private String roleTypeText;

    /**
     * 权限范围
     * JSON格式存储具体权限配置
     */
    @Schema(description = "权限范围")
    private String permissions;

    /**
     * 是否主要负责人
     * 1: 是, 0: 否
     */
    @Schema(description = "是否主要负责人")
    private Integer isPrimary;

    /**
     * 是否主要负责人文本（非数据库字段，需要转换）
     */
    @Schema(description = "是否主要负责人文本")
    private String isPrimaryText;

    /**
     * 状态
     * 1: 启用, 0: 禁用
     */
    @Schema(description = "状态")
    private Integer status;

    /**
     * 状态文本（非数据库字段，需要转换）
     */
    @Schema(description = "状态文本")
    private String statusText;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建人ID
     */
    @Schema(description = "创建人ID")
    private String creatorUserId;

    /**
     * 创建人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "创建人名称")
    private String creatorUserName;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date creatorTime;

    /**
     * 最后修改人ID
     */
    @Schema(description = "最后修改人ID")
    private String lastModifiedUserId;

    /**
     * 最后修改人名称（非数据库字段，需要关联查询）
     */
    @Schema(description = "最后修改人名称")
    private String lastModifiedUserName;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date lastModifiedTime;
}
