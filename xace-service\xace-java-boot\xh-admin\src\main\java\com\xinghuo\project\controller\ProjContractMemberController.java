package com.xinghuo.project.controller;

import com.xinghuo.common.base.ActionResult;
import com.xinghuo.common.base.MsgCode;
import com.xinghuo.common.util.JsonXhUtil;
import com.xinghuo.project.entity.ProjContractMemberEntity;
import com.xinghuo.project.model.contractmember.ProjContractMemberForm;
import com.xinghuo.project.model.contractmember.ProjContractMemberVO;
import com.xinghuo.project.service.ProjContractMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 项目合同成员控制器
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@Slf4j
@Tag(name = "项目合同成员管理", description = "项目合同成员管理相关接口")
@RestController
@RequestMapping("/api/project/contract/member")
public class ProjContractMemberController {

    @Autowired
    private ProjContractMemberService memberService;

    /**
     * 根据合同ID获取成员列表
     *
     * @param contractId 合同ID
     * @return 成员列表
     */
    @GetMapping("/contract/{contractId}")
    @Operation(summary = "根据合同ID获取成员列表")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
    })
    public ActionResult<List<ProjContractMemberVO>> listByContractId(@PathVariable("contractId") String contractId) {
        List<ProjContractMemberEntity> list = memberService.getListByContractId(contractId);
        List<ProjContractMemberVO> listVOs = JsonXhUtil.jsonToList(list, ProjContractMemberVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 根据用户ID获取参与的合同成员记录
     *
     * @param userId 用户ID
     * @return 成员记录列表
     */
    @GetMapping("/user/{userId}")
    @Operation(summary = "根据用户ID获取参与的合同成员记录")
    @Parameters({
            @Parameter(name = "userId", description = "用户ID", required = true),
    })
    public ActionResult<List<ProjContractMemberVO>> listByUserId(@PathVariable("userId") String userId) {
        List<ProjContractMemberEntity> list = memberService.getListByUserId(userId);
        List<ProjContractMemberVO> listVOs = JsonXhUtil.jsonToList(list, ProjContractMemberVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 根据合同ID和角色类型获取成员列表
     *
     * @param contractId 合同ID
     * @param roleType 角色类型
     * @return 成员列表
     */
    @GetMapping("/contract/{contractId}/role/{roleType}")
    @Operation(summary = "根据合同ID和角色类型获取成员列表")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "roleType", description = "角色类型", required = true),
    })
    public ActionResult<List<ProjContractMemberVO>> listByContractIdAndRoleType(
            @PathVariable("contractId") String contractId,
            @PathVariable("roleType") String roleType) {
        List<ProjContractMemberEntity> list = memberService.getListByContractIdAndRoleType(contractId, roleType);
        List<ProjContractMemberVO> listVOs = JsonXhUtil.jsonToList(list, ProjContractMemberVO.class);
        return ActionResult.success(listVOs);
    }

    /**
     * 获取合同成员详情
     *
     * @param id 成员ID
     * @return 成员详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "获取合同成员详情")
    @Parameters({
            @Parameter(name = "id", description = "成员ID", required = true),
    })
    public ActionResult<ProjContractMemberVO> info(@PathVariable("id") String id) {
        ProjContractMemberEntity entity = memberService.getById(id);
        ProjContractMemberVO vo = JsonXhUtil.jsonDeepCopy(entity, ProjContractMemberVO.class);
        return ActionResult.success(vo);
    }

    /**
     * 创建合同成员
     *
     * @param memberForm 成员表单
     * @return 操作结果
     */
    @PostMapping
    @Operation(summary = "创建合同成员")
    @Parameters({
            @Parameter(name = "memberForm", description = "成员表单", required = true),
    })
    public ActionResult create(@RequestBody @Valid ProjContractMemberForm memberForm) {
        // 检查用户是否已经是合同成员
        if (memberService.isContractMember(memberForm.getContractId(), memberForm.getUserId())) {
            return ActionResult.fail("该用户已经是合同成员");
        }

        ProjContractMemberEntity entity = JsonXhUtil.jsonDeepCopy(memberForm, ProjContractMemberEntity.class);
        memberService.create(entity);
        return ActionResult.success(MsgCode.SU001.get());
    }

    /**
     * 更新合同成员
     *
     * @param id 成员ID
     * @param memberForm 成员表单
     * @return 操作结果
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新合同成员")
    @Parameters({
            @Parameter(name = "id", description = "成员ID", required = true),
            @Parameter(name = "memberForm", description = "成员表单", required = true),
    })
    public ActionResult update(@PathVariable("id") String id, @RequestBody @Valid ProjContractMemberForm memberForm) {
        ProjContractMemberEntity entity = JsonXhUtil.jsonDeepCopy(memberForm, ProjContractMemberEntity.class);
        memberService.update(id, entity);
        return ActionResult.success(MsgCode.SU002.get());
    }

    /**
     * 删除合同成员
     *
     * @param id 成员ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除合同成员")
    @Parameters({
            @Parameter(name = "id", description = "成员ID", required = true),
    })
    public ActionResult delete(@PathVariable("id") String id) {
        memberService.delete(id);
        return ActionResult.success(MsgCode.SU003.get());
    }

    /**
     * 检查用户是否为合同成员
     *
     * @param contractId 合同ID
     * @param userId 用户ID
     * @return 是否为成员
     */
    @GetMapping("/check/{contractId}/{userId}")
    @Operation(summary = "检查用户是否为合同成员")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "userId", description = "用户ID", required = true),
    })
    public ActionResult<Boolean> isContractMember(
            @PathVariable("contractId") String contractId,
            @PathVariable("userId") String userId) {
        boolean isMember = memberService.isContractMember(contractId, userId);
        return ActionResult.success(isMember);
    }

    /**
     * 获取用户在指定合同中的角色类型
     *
     * @param contractId 合同ID
     * @param userId 用户ID
     * @return 角色类型
     */
    @GetMapping("/role/{contractId}/{userId}")
    @Operation(summary = "获取用户在指定合同中的角色类型")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "userId", description = "用户ID", required = true),
    })
    public ActionResult<String> getUserRoleInContract(
            @PathVariable("contractId") String contractId,
            @PathVariable("userId") String userId) {
        String roleType = memberService.getUserRoleInContract(contractId, userId);
        return ActionResult.success(roleType);
    }

    /**
     * 获取用户在指定合同中的权限
     *
     * @param contractId 合同ID
     * @param userId 用户ID
     * @return 权限JSON字符串
     */
    @GetMapping("/permissions/{contractId}/{userId}")
    @Operation(summary = "获取用户在指定合同中的权限")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "userId", description = "用户ID", required = true),
    })
    public ActionResult<String> getUserPermissionsInContract(
            @PathVariable("contractId") String contractId,
            @PathVariable("userId") String userId) {
        String permissions = memberService.getUserPermissionsInContract(contractId, userId);
        return ActionResult.success(permissions);
    }

    /**
     * 设置主要负责人
     *
     * @param contractId 合同ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @PutMapping("/primary/{contractId}/{userId}")
    @Operation(summary = "设置主要负责人")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "userId", description = "用户ID", required = true),
    })
    public ActionResult setPrimaryMember(
            @PathVariable("contractId") String contractId,
            @PathVariable("userId") String userId) {
        memberService.setPrimaryMember(contractId, userId);
        return ActionResult.success("设置主要负责人成功");
    }

    /**
     * 初始化合同默认成员
     *
     * @param contractId 合同ID
     * @param ownerId 合同负责人ID
     * @return 操作结果
     */
    @PostMapping("/init/{contractId}/{ownerId}")
    @Operation(summary = "初始化合同默认成员")
    @Parameters({
            @Parameter(name = "contractId", description = "合同ID", required = true),
            @Parameter(name = "ownerId", description = "合同负责人ID", required = true),
    })
    public ActionResult initDefaultMembers(
            @PathVariable("contractId") String contractId,
            @PathVariable("ownerId") String ownerId) {
        memberService.initDefaultMembers(contractId, ownerId);
        return ActionResult.success("初始化默认成员成功");
    }
}
